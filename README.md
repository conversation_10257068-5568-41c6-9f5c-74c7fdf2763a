# CD2GUI
Plugin providing Guidsl2 models to create and manage instances of a provided CD4A Model

## Include CD2GUI in your Application

- Parse the used domain file into a ASTCDCompilationUnit
- Create a CD2GUI instance using `CD2GUITool(ASTCDCompilationUnit domain, File targetFilepath, MCPath hwcPath, String... options)`
  
  - `domain` is the parsed domain file
  - output is provided to `targetFilepath`
  - `hwcPath` is used for handwritten extensions (unused at the moment)
  - `options` allow for the disabling of specific kinds of pages. Refer to the JavaDoc for more.

## Use CD2GUI with UMLP

- create a UMLPCdToGuiTask task with
  
  - `input` as the domain file
  - `outputDir` as the desired output directory
- create a UMLPGuiGenTask task with

  - `input` as the output from the UMLPCdToGuiTask
  - `guiCreateModelsAsFile` as `yourCD2GUIOutputDir/meta/guiCreateModels.txt`
  - the rest of the options are unchanged by cd2gui, please refer to the GUIDSL Documentation

## Supported CD2GUI Features

- Displaying instances of any valid Class Diagram both in overview and detailed view
- Changing primitive Attributes, as well as Optionals of them
- showing associations between the classes
- disabling individual pages or page categories

## Currently not supported CD2GUI Features

- adding or removing associations
- creating new instances of a class
- deleting instances of a class 
- editing complex Attributes

## Extending CD2GUI Pages with GUIVariability

- CD2GUI supports GUIVariability by adding meaningful component names refer to
- All named components can be replaced, deleted and altered with GUIVariability, for the specific naming system please refer to the relevant templates
- Please note that altering existing Pages might impact the functionality of both the page itself and other pages.
- For more information refer to GUIVariability

