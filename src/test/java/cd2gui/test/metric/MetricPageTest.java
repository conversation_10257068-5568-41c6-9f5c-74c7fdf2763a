package cd2gui.test.metric;


import static org.junit.Assert.*;

import cd2gui.test.parser.AbstractTest;
import org.junit.Test;

import java.io.File;
import java.io.IOException;

/**
 * Test class for metrics page extension in cd2gui
 */

public class MetricPageTest extends AbstractTest {

    static final String TARGET_PATH = "build/generated/test/cd2gui/";
    static final String HWC_PATH = "src/test/resources";


    //Test if metric page (.gui file) is generated
    @Test
    public void testCreateMetricsPageExecutionAndGuiFileGenerated() throws IOException {
        generateGUI("src/test/resources/Domain.cd", TARGET_PATH);
        parserTest(TARGET_PATH);
    }

}

