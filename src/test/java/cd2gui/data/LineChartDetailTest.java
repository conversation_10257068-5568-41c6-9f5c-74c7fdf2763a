package cd2gui.data;

import mc.fenix.charts.gemlinecharttypes.GemLineChartData;
import org.junit.Test;
import static org.junit.Assert.*;

import java.util.Arrays;

/**
 * Test class for LineChartDetail.
 * 
 */
public class LineChartDetailTest {

    @Test
    public void testDefaultConstructor() {
        LineChartDetail detail = new LineChartDetail();
        
        assertNotNull(detail.getData());
        assertFalse(detail.isBackgroundColorEnabled());
        assertEquals(100, detail.getMaxValue());
        assertEquals(0, detail.getMinValue());
    }

    @Test
    public void testAddEntry() {
        LineChartDetail detail = new LineChartDetail();
        detail.addEntry("Test Entry", Arrays.asList(1.0, 2.0, 3.0));

        GemLineChartData data = detail.getData();
        assertEquals(1, data.getEntriesList().size());
        assertEquals("Test Entry", data.getEntriesList().get(0).getLabel());
        assertEquals(3, data.getEntriesList().get(0).getDataList().size());
    }

    @Test
    public void testAddLabel() {
        LineChartDetail detail = new LineChartDetail();
        detail.addLabel("Label1");
        detail.addLabel("Label2");
        
        GemLineChartData data = detail.getData();
        assertEquals(2, data.getLabelsList().size());
        assertEquals("Label1", data.getLabelsList().get(0));
        assertEquals("Label2", data.getLabelsList().get(1));
    }

    @Test
    public void testSetData() {
        LineChartDetail detail = new LineChartDetail();
        GemLineChartData newData = new GemLineChartData();
        
        detail.setData(newData);
        
        assertEquals(newData, detail.getData());
    }

    @Test
    public void testBackgroundColorProperty() {
        LineChartDetail detail = new LineChartDetail();
        
        assertFalse(detail.isBackgroundColorEnabled());
        
        detail.setEnableBackgroundColor(true);
        assertTrue(detail.isBackgroundColorEnabled());
        
        detail.setEnableBackgroundColor(false);
        assertFalse(detail.isBackgroundColorEnabled());
    }

    @Test
    public void testMaxMinValues() {
        LineChartDetail detail = new LineChartDetail();
        
        assertEquals(100, detail.getMaxValue());
        assertEquals(0, detail.getMinValue());
        
        detail.setMaxValue(200);
        detail.setMinValue(10);
        
        assertEquals(200, detail.getMaxValue());
        assertEquals(10, detail.getMinValue());
    }

    @Test
    public void testMultipleEntries() {
        LineChartDetail detail = new LineChartDetail();
        detail.addEntry("Entry1", Arrays.asList(1.0, 2.0));
        detail.addEntry("Entry2", Arrays.asList(3.0, 4.0));
        detail.addEntry("Entry3", Arrays.asList(5.0, 6.0));

        GemLineChartData data = detail.getData();
        assertEquals(3, data.getEntriesList().size());

        assertEquals("Entry1", data.getEntriesList().get(0).getLabel());
        assertEquals("Entry2", data.getEntriesList().get(1).getLabel());
        assertEquals("Entry3", data.getEntriesList().get(2).getLabel());
    }
}
