<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "attributeMetric")}

<#assign chartContent>
<#if attributeMetric??>
  <#assign chartType = attributeMetric.getType()>
  <#-- Route to appropriate chart component based on recommended chart type -->
  <#switch chartType>
    <#case "PIE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.pie-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "BAR_CHART">
      ${tc.includeArgs("tpl.metrics.charts.bar-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "LINE_CHART">
      ${tc.includeArgs("tpl.metrics.charts.line-chart", [domainClass, name, attributeMetric])}
      <#break>

    <#case "SCATTER_PLOT">
      ${tc.includeArgs("tpl.metrics.charts.scatter-plot", [domainClass, name, attributeMetric])}
      <#break>

    <#default>
      ${tc.includeArgs("tpl.metrics.charts.text-display-component", [domainClass, name, attributeMetric])}
  </#switch>
<#elseif attributeMetric??>
  <#-- No recommended chart or not visualizable - use text display -->
  ${tc.includeArgs("tpl.metrics.charts.text-display-component", [domainClass, name, attributeMetric])}
<#else>
  <#-- Debug: Show AttributeMetric status -->
  @GemText(
    value = "DEBUG: <#if attributeMetric??>Attr: ${attributeMetric.getAttributeName()}, Type: ${attributeMetric.getType()}, Visualizable: ${attributeMetric.isVisualizableAttribute()}<#else>AttributeMetric is null</#if>"
  )
</#if>
</#assign>





<#if attributeMetric??>
${name?uncap_first}_${attributeMetric.getAttributeName()}ChartCard@GemCard(
  width = "auto",
  height = "auto",
  title = "${attributeMetric.getAttributeName()} Analysis",
  component = @GemColumn(
    hAlign = "center",
    vAlign = "center",
    components = [
      ${chartContent}
    ]
  )
)
<#else>
${name?uncap_first}_NoDataChartCard@GemCard(
  width = "auto",
  height = "auto",
  title = "No Data Available",
  component = @GemColumn(
    hAlign = "center",
    vAlign = "center",
    components = [
      @GemText(
        value = "No metric data available for this attribute"
      )
    ]
  )
)
</#if>
