${tc.signature("domainClass", "name", "attributeMetric")}

<#--
text-display-component.ftl for metrics
Text display component for attributes that don't have suitable chart visualization.
This component provides a clean text-based representation of attribute information.

Parameters
- domainClass     : ASTCDClass        → The domain class
- name           : String            → Class name
- attributeMetric : AttributeMetric   → The attribute metric data

-->

<#if attributeMetric??>
@GemColumn(
  hAlign = "center",
  vAlign = "center",
  rowGap = "10px",
  components = [

    <#-- Attribute name as main display -->
    @GemText(value = "${attributeMetric.getAttributeName()}"),

    <#-- Chart type information -->
    @GemText(value = "Chart Type: ${attributeMetric.getType()}"),

    <#-- Visualization status -->
    @GemText(value = <#if attributeMetric.isVisualizableAttribute()>"Visualizable but no suitable chart available"<#else>"Not suitable for visualization"</#if>)

  ]
)
<#else>
@GemText(value = "No attribute metric data available")
</#if>
