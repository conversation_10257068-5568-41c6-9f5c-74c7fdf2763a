<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "attributeMetric")}

<#if attributeMetric??>
${name?uncap_first}_${attributeMetric.getAttributeName()}Table@GemTable(
  width = "100%",
  height = "auto",
  layout = "auto",
  headers = [
    @GemTableHeader(cols = ["Property", "Value"])
  ],
  body = [
    <#-- Attribute name row -->
    @GemTableRow(cols = [
      @GemText(value = "Attribute Name"),
      @GemText(value = "${attributeMetric.getAttributeName()}")
    ]),

    <#-- Chart type row -->
    @GemTableRow(cols = [
      @GemText(value = "Chart Type"),
      @GemText(value = "${attributeMetric.getType()}")
    ]),

    <#-- Visualization status row -->
    @GemTableRow(cols = [
      @GemText(value = "Visualizable"),
      @GemText(value = "${attributeMetric.isVisualizableAttribute()?string('Yes', 'No')}")
    ])
  ]
)
<#else>
@GemText(value = "No attribute metric data available")
</#if>
