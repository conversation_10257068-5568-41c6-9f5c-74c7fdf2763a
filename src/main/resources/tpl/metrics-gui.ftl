<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "domainPackage", "classMetrics")}

<#-- Complete metrics page GUI file -->
package ${domainPackage?lower_case};

${tc.includeArgs("tpl.metrics.imports-metrics", [domainClass, name, classMetrics])}

page ${name}Metric() {

  ${tc.includeArgs("tpl.metrics.visualization-panel", [domainClass, name, classMetrics])}

}
