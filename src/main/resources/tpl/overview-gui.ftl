<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "domainPackage", "attributes", "subclasses")}
/* (c) https://github.com/MontiCore/monticore */
package ${domainPackage?lower_case};

//data classes
import ${domainPackage}.${name};

//GUI models
${tc.includeArgs("tpl.overview.imports-overview", [domainClass, name, attributes])}

page ${name}Overview(List<${name}> ${name?lower_case}s) {

    ${tc.includeArgs("tpl.overview.card-overview", [domainClass, name, attributes])}
    <#if subclasses?size != 0 >
    ${tc.includeArgs("tpl.overview.subclass-overview", [domainClass, name, subclasses])}
    </#if>

    @GemNavItem(target = "/${domainPackage?lower_case}/${name}Form/", title = "Create new");
}