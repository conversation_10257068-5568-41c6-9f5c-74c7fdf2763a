package cd2gui.metric;

import cd2gui.data.AttributeMetric;
import cd2gui.data.CD2GUIAttribute;
import de.monticore.cdbasis._ast.ASTCDClass;

import java.util.*;

/**
 * Stage2 entry point. Processes UML class data and generates chart specifications.
 * Calls calculator.UnifiedAttributeAnalyzer for core analysis logic.
 *
 * <AUTHOR>
 */
public class AttributeMetricIdentifier {

    private final UnifiedAttributeAnalyzer analyzer;

    /**
     * Creates identifier with UnifiedAttributeAnalyzer instance.
     *
     * <AUTHOR>
     */
    public AttributeMetricIdentifier() {
        this.analyzer = new UnifiedAttributeAnalyzer();
    }

    /**
     * Main Stage2 entry point. Directly uses CD2GUIAttribute list from attributeMap.
     *
     * @param clazz UML class structure from Stage1
     * @param attributes CD2GUIAttribute list from GuiModelFileCreator.attributeMap
     * @return list of AttributeMetric objects for Stage3
     * <AUTHOR>
     */
    public List<AttributeMetric<?>> processClassForVisualization(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        if (clazz == null || attributes == null) {
            return new ArrayList<>();
        }

        return analyzer.analyzeClassForVisualization(clazz, attributes);
    }

    /**
     * Returns chart count for given class and attributes.
     *
     * @param clazz UML class structure
     * @param attributes CD2GUIAttribute list from GuiModelFileCreator.attributeMap
     * @return number of charts generated
     * <AUTHOR> Yang
     */
    public int getChartCount(ASTCDClass clazz, List<CD2GUIAttribute> attributes) {
        return processClassForVisualization(clazz, attributes).size();
    }

    /**
     * Gets the unified analyzer instance.
     *
     * @return the analyzer
     * <AUTHOR> Yang
     */
    public UnifiedAttributeAnalyzer getAnalyzer() {
        return analyzer;
    }
}
