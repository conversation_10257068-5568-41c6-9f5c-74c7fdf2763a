package cd2gui.data;

/**
 * Measurement scale enumeration. Used by UnifiedAttributeAnalyzer.determineScale().
 * Maps to chart types: NOMINAL→PIE, ORDINAL→BAR, INTERVAL→LINE, RATIO→SCATTER.
 *
 * <AUTHOR> Yang
 */
public enum MetricScale {
    /**
     * Categorical data without order. Maps to PIE_CHART.
     */
    NOMINAL("Nominal Scale", "Categorical data with no inherent order"),

    /**
     * Ordered categorical data. Maps to BAR_CHART.
     */
    ORDINAL("Ordinal Scale", "Ordered categorical data"),

    /**
     * Equal-interval data without absolute zero. Maps to LINE_CHART.
     */
    INTERVAL("Interval Scale", "Equal-interval data with no absolute zero"),

    /**
     * Continuous data with absolute zero. Maps to SCATTER_PLOT.
     */
    RATIO("Ratio Scale", "Continuous data with absolute zero"),

    /**
     * Not suitable for metrics - Attributes that are not appropriate as metric indicators.
     * Examples: identifiers, large text fields, complex objects
     */
    NONE("Not suitable for metrics", "Not suitable as metric indicator");

    private final String displayName;
    private final String description;

    /**
     * Constructor for MetricScale enum.
     *
     * @param displayName human-readable name for the scale
     * @param description detailed description of the scale characteristics
     */
    MetricScale(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * Get the human-readable display name for this measurement scale.
     *
     * @return display name of the scale
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * Get the detailed description of this measurement scale.
     *
     * @return description of the scale characteristics
     */
    public String getDescription() {
        return description;
    }

    /**
     * Check if this scale is suitable for metric analysis.
     *
     * @return true if the scale can be used for metrics, false otherwise
     */
    public boolean isMetricSuitable() {
        return this != NONE;
    }

    /**
     * Check if this scale represents ordered data.
     *
     * @return true if the scale has inherent ordering (ordinal, interval, ratio)
     */
    public boolean isOrdered() {
        return this == ORDINAL || this == INTERVAL || this == RATIO;
    }

    /**
     * Check if this scale represents numerical data.
     *
     * @return true if the scale represents numerical data (interval, ratio)
     */
    public boolean isNumerical() {
        return this == INTERVAL || this == RATIO;
    }

    /**
     * Get all metric-suitable scales.
     *
     * @return list of scales suitable for metric analysis
     */
    public static java.util.List<MetricScale> getSupportedScales() {
        return java.util.Arrays.stream(values())
                .filter(MetricScale::isMetricSuitable)
                .collect(java.util.stream.Collectors.toList());
    }
}
