package cd2gui.data;

/**
 * Stage2 output container. Contains ChartType and ChartDetail for Stage3 rendering.
 *
 * @param <D> chart data type (GemPieChartData, GemBarChartData, etc.)
 * <AUTHOR> Yang
 */
public class AttributeMetric<D> {
    private final String attributeName;
    private final Boolean isVisualizable;
    private final ChartType type;
    private final ChartDetail<D> detail;

    /**
     * Creates AttributeMetric with ChartType and ChartDetail.
     *
     * @param type chart type (PIE_CHART, BAR_CHART, LINE_CHART, SCATTER_PLOT)
     * @param detail chart detail implementation
     */
    public AttributeMetric(String attributeName, ChartType type, ChartDetail<D> detail) {
        this.attributeName = attributeName;
        this.isVisualizable = type.isVisualizationSuitable();
        this.type = type;
        this.detail = detail;
    }

    /**
     * Returns attribute name for Stage3 rendering.
     * @return attribute name
     */
    public String getAttributeName() {
        return attributeName;
    }

    /**
     * Returns chart type for Stage3 rendering.
     *
     * @return chart type
     */
    public ChartType getType() {
        return type;
    }

    /**
     * Returns chart detail containing mc.fenix.charts data.
     *
     * @return chart detail
     */
    public ChartDetail<D> getDetail() {
        return detail;
    }

    /**
     * Checks if chart is suitable for visualization.
     *
     * @return true if visualizable
     */
    public Boolean isVisualizableAttribute() {
        return isVisualizable;
    }

    @Override
    public String toString() {
        return "AttributeMetric{" +
                "attributeName='" + attributeName +
                ", type=" + type +
                ", isVisualizable=" + isVisualizable +
                '}';
    }
}
