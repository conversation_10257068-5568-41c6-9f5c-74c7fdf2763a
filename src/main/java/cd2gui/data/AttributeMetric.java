package cd2gui.data;

/**
 * @param <D> chart data type (GemPieChartData, GemBarChartData, etc.)
 */

public class AttributeMetric<D> {
    private final String attributeName;
    private final Boolean isVisualizable;
    private final ChartType recommendedType;
    private final ChartDetail<D> chartDetail;

    /**
     * Creates AttributeMetric with ChartType and ChartDetail.
     *
     * @param chartType chart type (PIE_CHART, BAR_CHART, LINE_CHART, SCATTER_PLOT)
     * @param chartDetail chart detail implementation
     */
    public AttributeMetric(String attributeName, ChartType chartType, ChartDetail<D> chartDetail) {
        this.attributeName = attributeName;
        this.isVisualizable = chartType.isVisualizationSuitable();
        this.recommendedType = chartType;
        this.chartDetail = chartDetail;
    }

    /**
     * Returns attribute name for Stage3 rendering.
     * @return attribute name
     */
    public String getAttributeName() {
        return attributeName;
    }

    /**
     * Returns chart type for Stage3 rendering.
     *
     * @return chart type
     */
    public ChartType getType() {
        return recommendedType;
    }

    /**
     * Returns chart detail containing mc.fenix.charts data.
     *
     * @return chart detail
     */
    public ChartDetail<D> getDetail() {
        return chartDetail;
    }

    /**
     * Checks if the chart is suitable for visualization.
     *
     * @return true if visualizable
     */
    public Boolean isVisualizableAttribute() {
        return isVisualizable;
    }

    @Override
    public String toString() {
        return "AttributeMetric{" +
                "attributeName='" + attributeName +
                ", type=" + recommendedType +
                ", isVisualizable=" + isVisualizable +
                '}';
    }
}
