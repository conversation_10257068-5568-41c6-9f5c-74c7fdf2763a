package cd2gui.data;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Container for AttributeMetric list. Used by Stage3 template processing.
 *
 */
public class ClassMetrics {
    private final List<AttributeMetric<?>> attributeMetrics;
    private final Integer totalAttributesInt;

    /**
     * Creates ClassMetrics filtering visualizable AttributeMetric objects.
     *
     * @param attributeMetrics list of AttributeMetric from Stage2
     */
    public ClassMetrics(List<AttributeMetric<?>> attributeMetrics) {
        this.attributeMetrics = new ArrayList<>();
        if (attributeMetrics != null) {
            for (AttributeMetric<?> metric : attributeMetrics) {
                if (metric.isVisualizableAttribute()) {
                    this.attributeMetrics.add(metric);
                }
            }
        }
        this.totalAttributesInt = this.attributeMetrics.size();
    }

    /**
     * Checks if has visualizable attributes.
     *
     * @return true if has visualizable attributes
     */
    public boolean hasVisualizableAttributes() {
        return !attributeMetrics.isEmpty();
    }

    /**
     * Gets all attribute metrics.
     *
     * @return the attribute metrics
     */
    public List<AttributeMetric<?>> getAttributeMetrics() {
        return new ArrayList<>(attributeMetrics);
    }

    /**
     * Gets unique attribute metrics (deduplicated by attribute name).
     * Prevents duplicate chart components in GUI templates.
     *
     * @return the unique attribute metrics
     */
    public List<AttributeMetric<?>> getUniqueAttributeMetrics() {
        Map<String, AttributeMetric<?>> uniqueMetrics = new LinkedHashMap<>();
        for (AttributeMetric<?> metric : attributeMetrics) {
            if (metric != null && metric.getAttributeName() != null) {
                // Keep the first occurrence of each attribute
                uniqueMetrics.putIfAbsent(metric.getAttributeName(), metric);
            }
        }
        return new ArrayList<>(uniqueMetrics.values());
    }

    /**
     * Gets total number of attributes.
     * 
     * @return the total number of attributes
     */
    public Integer getTotalAttributesInt() {
        return totalAttributesInt;
    }

    @Override
    public String toString() {
        return "ClassMetrics{" +
                "attributeMetrics=" + attributeMetrics.size() + " metrics" +
                ", totalAttributesInt=" + totalAttributesInt +
                ", hasVisualizableAttributes=" + hasVisualizableAttributes() +
                '}';
    }
}
