package cd2gui.data;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * This class serves as a dedicated wrapper for passing to the template file.
 *  When adding elements to attributeMetrics, check if they are visualizable.
 *  Ensure that the isVisualizable property of the wrapped AttributeMetric is always true.
 */

public class ClassMetrics {
    private final List<AttributeMetric<?>> attributeMetrics;
    private final Integer totalAttributesInt;

    /**
     * Create a classMetrics object and filter out non-visualizable attributeMetrics.
     *
     * @param attributeMetrics list of AttributeMetrics that has been analyzed.
     */
    public ClassMetrics(List<AttributeMetric<?>> attributeMetrics) {
        this.attributeMetrics = new ArrayList<>();
        if (attributeMetrics != null && !attributeMetrics.isEmpty()) {
            for (AttributeMetric<?> metric : attributeMetrics) {
                if (metric.isVisualizableAttribute()) {
                    this.attributeMetrics.add(metric);
                }
            }
        }
        this.totalAttributesInt = this.attributeMetrics.size();
    }

    /**
     * Check whether there are visualizable metrics.
     *
     * @return true if it has visualizable attributes
     */
    public boolean hasVisualizableAttributes() {
        return !attributeMetrics.isEmpty();
    }

    /**
     * Gets all attribute metrics.
     *
     * @return the attribute metrics
     */
    public List<AttributeMetric<?>> getAttributeMetrics() {
        return new ArrayList<>(attributeMetrics);
    }

    /**
     * Gets unique attribute metrics (deduplicated by attribute name).
     * Prevents duplicate chart components in GUI templates.
     *
     * @return the unique attribute metrics
     */
    public List<AttributeMetric<?>> getUniqueAttributeMetrics() {
        Map<String, AttributeMetric<?>> uniqueMetrics = new LinkedHashMap<>();
        for (AttributeMetric<?> metric : attributeMetrics) {
            if (metric != null && metric.getAttributeName() != null) {
                // Keep the first occurrence of each attribute
                uniqueMetrics.putIfAbsent(metric.getAttributeName(), metric);
            }
        }
        return new ArrayList<>(uniqueMetrics.values());
    }

    /**
     * Gets total number of attributes.
     *
     * @return the total number of attributes
     */
    public Integer getTotalAttributesInt() {
        return totalAttributesInt;
    }

    @Override
    public String toString() {
        return "ClassMetrics{" +
                "attributeMetrics=" + attributeMetrics +
                ", totalAttributesInt=" + totalAttributesInt +
                ", hasVisualizableAttributes=" + hasVisualizableAttributes() +
                '}';
    }
}
