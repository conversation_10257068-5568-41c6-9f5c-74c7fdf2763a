package cd2gui.data;

/**
 * Chart type enumeration. Used by UnifiedAttributeAnalyzer for chart type selection.
 * Core types: PIE_CHART, BAR_CHART, LINE_CHART, SCATTER_PLOT.
 *
 * <AUTHOR>
 */
public enum ChartType {
    /**
     * Pie chart for categorical data. Used by geographic and academic patterns.
     */
    PIE_CHART("Pie Chart", "Suitable for nominal scale categorical data"),
    
    /**
     * Bar chart for comparison data. Used by performance and financial patterns.
     */
    BAR_CHART("Bar Chart", "Suitable for categorical and ordinal data"),
    
    /**
     * Line chart for temporal data. Used by temporal pattern recognition.
     */
    LINE_CHART("Line Chart", "Suitable for time series data"),
    
    /**
     * Scatter plot for correlation analysis. Used by correlation pattern recognition.
     */
    SCATTER_PLOT("Scatter Plot", "Suitable for correlation analysis"),
    
    /**
     * Heatmap Chart - Suitable for visualizing data density or patterns in a matrix format.
     * Best for: showing relationships between two categorical variables
     * UMLP Component: GemHeatmapChart
     */
    HEATMAP_CHART("Heatmap Chart", "Suitable for matrix data visualization"),

    /**
     * Bullet Chart - Suitable for displaying performance metrics with targets.
     * Best for: showing progress towards goals, performance indicators
     * UMLP Component: GemBulletChart
     */
    BULLET_CHART("Bullet Chart", "Suitable for performance metrics with targets"),

    /**
     * Candlestick Chart - Suitable for displaying financial data with open/close/high/low values.
     * Best for: financial data visualization, stock price movements
     * UMLP Component: GemCandlestickChart
     */
    CANDLESTICK_CHART("Candlestick Chart", "Suitable for financial data visualization"),

    /**
     * Gauge Chart - Suitable for displaying single values with context.
     * Best for: showing progress, performance metrics within a range
     * UMLP Component: GemGaugeChart
     */
    GAUGE_CHART("Gauge Chart", "Suitable for single value display with context"),
    
    /**
     * Radar Chart - Suitable for multivariate data comparison.
     * Best for: comparing multiple attributes across categories
     * UMLP Component: GemRadarChart
     */
    RADAR_CHART("Radar Chart", "Suitable for multivariate data comparison"),
    
    /**
     * Sunburst Chart - Suitable for hierarchical data visualization.
     * Best for: showing part-to-whole relationships in hierarchical data
     * UMLP Component: GemSunburstChart
     */
    SUNBURST_CHART("Sunburst Chart", "Suitable for hierarchical data display"),
    
    /**
     * Network Graph - Suitable for displaying relationships between entities.
     * Best for: showing connections, networks, graph structures
     * UMLP Component: GemNetworkGraph
     */
    NETWORK_GRAPH("Network Graph", "Suitable for relationship visualization"),
    
    /**
     * Venn Diagram - Suitable for displaying set relationships and overlaps.
     * Best for: showing logical relationships between sets
     * UMLP Component: GemVennDiagram
     */
    VENN_DIAGRAM("Venn Diagram", "Suitable for set relationship visualization"),
    
    /**
     * Not recommended - Attributes that are not suitable for chart visualization.
     * Used for: complex objects, identifiers, unsuitable data types
     */
    NONE("Not recommended", "Not suitable for chart visualization");

    private final String displayName;
    private final String description;

    /**
     * Constructor for ChartType enum.
     * 
     * @param displayName human-readable name for the chart type
     * @param description detailed description of the chart type usage
     */
    ChartType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * Get the human-readable display name for this chart type.
     * 
     * @return display name of the chart type
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * Get the detailed description of this chart type.
     * 
     * @return description of the chart type usage and characteristics
     */
    public String getDescription() {
        return description;
    }

    /**
     * Check if this chart type is suitable for visualization.
     * 
     * @return true if the chart type can be used for visualization, false otherwise
     */
    public boolean isVisualizationSuitable() {
        return this != NONE;
    }

    /**
     * Check if this chart type is suitable for categorical data.
     * 
     * @return true if the chart type works well with categorical data
     */
    public boolean isCategoricalSuitable() {
        return this == PIE_CHART || this == BAR_CHART;
    }

    /**
     * Check if this chart type is suitable for numerical data.
     *
     * @return true if the chart type works well with numerical data
     */
    public boolean isNumericalSuitable() {
        return this == LINE_CHART || this == SCATTER_PLOT ||
               this == HEATMAP_CHART || this == GAUGE_CHART ||
               this == BULLET_CHART || this == CANDLESTICK_CHART;
    }

    /**
     * Parse chart type from string representation.
     * 
     * @param chartTypeName string representation of chart type
     * @return corresponding ChartType enum, or NONE if not found
     */
    public static ChartType fromString(String chartTypeName) {
        if (chartTypeName == null || chartTypeName.trim().isEmpty()) {
            return NONE;
        }
        
        String normalized = chartTypeName.toLowerCase().trim();
        
        switch (normalized) {
            case "pie":
            case "pie_chart":
            case "piechart":
                return PIE_CHART;
            case "bar":
            case "bar_chart":
            case "barchart":
                return BAR_CHART;
            case "line":
            case "line_chart":
            case "linechart":
                return LINE_CHART;
            case "scatter":
            case "scatter_plot":
            case "scatterplot":
                return SCATTER_PLOT;
            case "heatmap":
            case "heatmap_chart":
            case "heatmapchart":
                return HEATMAP_CHART;
            case "bullet":
            case "bullet_chart":
            case "bulletchart":
                return BULLET_CHART;
            case "candlestick":
            case "candlestick_chart":
            case "candlestickchart":
                return CANDLESTICK_CHART;
            case "gauge":
            case "gauge_chart":
            case "gaugechart":
                return GAUGE_CHART;
            case "radar":
            case "radar_chart":
            case "radarchart":
                return RADAR_CHART;
            case "sunburst":
            case "sunburst_chart":
            case "sunburstchart":
                return SUNBURST_CHART;
            case "network":
            case "network_graph":
            case "networkgraph":
                return NETWORK_GRAPH;
            case "venn":
            case "venn_diagram":
            case "venndiagram":
                return VENN_DIAGRAM;
            case "none":
            case "not_recommended":
                return NONE;
            default:
                return NONE;
        }
    }
}
