/* (c) https://github.com/MontiCore/monticore */
package cd2gui;

import cd2gui.data.CD2GUIClassTreeNode;
import cd2gui.ftl.FTLGenerator;
import cd2gui.util.*;
import com.google.common.collect.Lists;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.monticore.cdbasis._ast.ASTCDClassTOP;
import de.monticore.cdbasis._ast.ASTCDCompilationUnit;
import de.monticore.generating.GeneratorSetup;
import de.monticore.generating.templateengine.GlobalExtensionManagement;
import de.monticore.io.paths.MCPath;
import de.se_rwth.commons.Joiners;
import de.se_rwth.commons.Names;
import de.se_rwth.commons.logging.Log;

import java.io.File;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public class CD2GUITool {

  private final ASTCDCompilationUnit astCDCompUnit;

  private List<ASTCDClass> classes;

  private final File targetFilepath;

  private final MCPath hwcPath;

  private GuiModelFileCreator guiModelFileCreator;

  private String domainPackage;

  private List<CD2GUIClassTreeNode> classTrees;

  private static List<String> options;

  private final GeneratorSetup setup;

  /**
   * CD2GUI allows the generation of gui-pages that can display, create and alter instances of classes in the classdiagram
   * @param domain The parsed domain file of the application
   * @param targetFilepath The Folder where the gui-models are generated into.
   * @param hwcPath The Path of handwritten files
   * @param options Options for generation, currently supported are "NoDashboard", "NoOverview", "NoDetails", "NoDetailsEdit", "NoForm", "NOMetrics"
   *                if these flags are set, the corresponding Page won't be generated.
   *                Additionally, "ExcludeForm", followed by  the domain class fqn is supported to exclude the generation of forms for specific classes
   */
  public CD2GUITool(ASTCDCompilationUnit domain, File targetFilepath, MCPath hwcPath, String... options){
    CD2GUITool.options = List.of(options);
    this.targetFilepath = targetFilepath;
    this.hwcPath = hwcPath;
    this.astCDCompUnit = domain;

    if(astCDCompUnit.isPresentMCPackageDeclaration()) {
      domainPackage = astCDCompUnit.getMCPackageDeclaration().getMCQualifiedName().toString() + "." + astCDCompUnit.getCDDefinition().getName();
    }
    else {
      domainPackage = astCDCompUnit.getCDDefinition().getName();
    }
    setup = new GeneratorSetup();
    setup.setOutputDirectory(targetFilepath);
    setup.setTracing(false);
    GlobalExtensionManagement glex = new GlobalExtensionManagement();
    glex.setGlobalValue("attrManager", new AttributeManager());
    glex.setGlobalValue("roleManager", new RoleManager());
    glex.setGlobalValue("nameTransformer", new NameTransformer());
    glex.setGlobalValue("linkPath", Names.getPathFromQualifiedName(
            Joiners.DOT.join(domainPackage, astCDCompUnit.getCDDefinition().getName())).toLowerCase());
    setup.setGlex(glex);

  }

  /**
   * CD2GUI allows the generation of gui-pages that can display, create and alter instances of classes in the classdiagram
   * @param domain The parsed domain file of the application
   * @param targetFilepath The Folder where the gui-models are generated into.
   * @param hwcPath The Path of handwritten files
   * @param templatePath alternate Locations where the generator will look for templates this simplifies the template replacement
   * @param options Options for generation, currently supported are "NoDashboard", "NoOverview", "NoDetails", "NoDetailsEdit", "NoForm", "NOMetrics"
   *                if these flags are set, the corresponding Page won't be generated.
   */
  public CD2GUITool(ASTCDCompilationUnit domain, File targetFilepath, MCPath hwcPath, File templatePath, String... options){
    this(domain, targetFilepath, hwcPath, options);

    setup.setAdditionalTemplatePaths(Lists.newArrayList(templatePath));

  }

  public void generateGUI() {

    Log.info("       .oooooo.   oooooooooo.     .oooo.     .oooooo.    ooooo     ooo ooooo \n" +
            "      d8P'  `Y8b  `888'   `Y8b  .dP\"\"Y88b   d8P'  `Y8b   `888'     `8' `888' \n" +
            "     888           888      888       ]8P' 888            888       8   888  \n" +
            "     888           888      888     .d8P'  888            888       8   888  \n" +
            "     888           888      888   .dP'     888     ooooo  888       8   888  \n" +
            "     `88b    ooo   888     d88' .oP     .o `88.    .88'   `88.    .8'   888  \n" +
            "      `Y8bood8P'  o888bood8P'   8888888888  `Y8bood8P'      `YbodP'    o888o ", "cd2gui");

    Log.debug("--------------------------------", "cd2gui");
    Log.debug( "\n" +
                    "Output dir     : " + targetFilepath + "\n" +
                    "HWC dir        : " + hwcPath,
            "cd2gui"
    );
    Log.info("--------------------------------", "cd2gui");

    generateAST();

    classTrees = ASTCDClassManager.buildSubclassTrees(astCDCompUnit.getCDDefinition().getCDClassesList());

    // -----------------------------------------------
    // Create GUI Model Files
    // -----------------------------------------------

    Log.info("creating gui-models", "cd2gui");

    guiModelFileCreator = new GuiModelFileCreator(classes, classTrees, setup, astCDCompUnit.getCDDefinition().getName(), domainPackage);

    try {
      if(!options.contains("NoDashboard"))
        guiModelFileCreator.createDashboard();
      if(!options.contains("NoOverview"))
        guiModelFileCreator.createOverviewPages();
      if(!options.contains("NoDetails"))
        guiModelFileCreator.createDetailsPages();
      if(!options.contains("NoDetailsEdit"))
        guiModelFileCreator.createDetailsEditPages();
      if(!options.contains("NoMetrics"))
        guiModelFileCreator.createMetricsPage();
      if(!options.contains("NoForm")) {
        List<String> excludedForms = new ArrayList<>();
        String last = null;
        for (String o : options) {
          if("ExcludeForm".equals(last)){
            excludedForms.add(o);
          }

          last = o;
        }
        guiModelFileCreator.createFormPages(excludedForms);
      }
    }
    catch (Exception e) {
      Log.error("CD2GUI0x200 writing a gui file fails. " + e.getMessage());
    }

    // -----------------------------------------------
    // Generate FTL Templates for UMLP-Tool
    // -----------------------------------------------

    Log.info("generating FTL templates for UMLP-Tool", "cd2gui");

    try {
      FTLGenerator.generateFTLTemplates(guiModelFileCreator.getMetricsMap());
      Log.info("FTL template generation completed successfully", "cd2gui");
    }
    catch (Exception e) {
      Log.error("FTL template generation failed: " + e.getMessage());
    }
  }

  /**
   * Generating the ASTs for the domain classdiagram and setting up the package for DataClass imports
   */
  public void generateAST(){

    classes = astCDCompUnit.getCDDefinition().getCDClassesList().stream()
                .filter(ASTCDClassManager::hasPage)
                .sorted(Comparator.comparing(ASTCDClassTOP::getName))
                .collect(Collectors.toList());

    Types.setClassesWithDetailsPage(classes.stream().map(ASTCDClass::getName).collect(Collectors.toList()));

    //the same for now, should change when abstract classes have an overview page
    Types.setClassesWithOverviewPage(classes.stream().map(ASTCDClass::getName).collect(Collectors.toList()));

    Log.info("cd Loaded", "CD2GUITool");

  }

  public GuiModelFileCreator getGuiModelFileCreator() {
    //used only for Debugging or Tests
    return guiModelFileCreator;
  }

  public List<ASTCDClass> getClasses() {
    //used only for Debugging or Tests
    return classes;
  }

  public List<CD2GUIClassTreeNode> getClassTrees() {
    //used only for Debugging or Tests
    return classTrees;
  }

  public String getDomainPackage() {
    //used only for Debugging or Tests
    return domainPackage;
  }

  public void setGuiModelFileCreator(GuiModelFileCreator guiModelFileCreator) {
    //used only for Debugging or Tests
    this.guiModelFileCreator = guiModelFileCreator;
  }

  public static boolean isOption(String option){
    return CD2GUITool.options.contains(option);
  }
}
