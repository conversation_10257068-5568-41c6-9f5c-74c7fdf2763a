# SLE25 Metrics2GUI 项目测试指南（Testing Guide）

## 测试框架与工具

### 核心测试框架

* **JUnit**：项目使用 JUnit 作为主要测试框架
* **断言库**：使用 JUnit 内置的 Assert 类进行断言
* **构建集成**：测试通过 Gradle 构建系统执行

### 依赖配置

项目在 `build.gradle` 中配置了以下测试相关依赖：

```gradle
testImplementation group: 'junit', name: 'junit', version: junit_version
testImplementation group: 'ch.qos.logback', name: 'logback-classic', version: '1.4.5'
testImplementation group: 'de.monticore.lang', name: 'guidsl', version: mc_version
```

---

## 测试目录结构与组织

### 标准测试结构

项目遵循 Gradle 标准测试目录结构：

```
src/test/
├── java/                           # 测试源代码
│   └── cd2gui/
│       └── test/
│           └── parser/             # 解析器相关测试
│               ├── AbstractTest.java        # 抽象测试基类
│               ├── ComponentTest.java       # 组件测试
│               ├── ClassTreeTest.java       # 类树测试
│               ├── DerivedTest.java         # 派生类测试
│               ├── ExcludeFormTest.java     # 表单排除测试
│               ├── InvisibleTest.java       # 不可见元素测试
│               ├── PartsTest.java           # 部件测试
│               ├── SuperclassTest.java      # 超类测试
│               └── TemplateTest.java        # 模板测试
└── resources/                      # 测试资源文件
    ├── ClassTreeTest.cd            # 类树测试模型
    ├── DerivedTest.cd              # 派生测试模型
    ├── Domain.cd                   # 领域模型
    ├── InvisibleTest.cd            # 不可见测试模型
    ├── Sehub.cd                    # Sehub测试模型
    ├── SuperClassTest.cd           # 超类测试模型
    └── rpl/                        # 模板替换测试文件夹
        └── tpl/
            └── overview/
                └── attributes-overview.ftl
```

### 测试包命名规范

* **测试基类**：`cd2gui.test.parser.AbstractTest` - 提供通用测试功能
* **功能测试**：`cd2gui.test.parser.*Test` - 具体功能测试类
* **测试资源**：`src/test/resources/` - 测试用例文件和模板

### 测试文件命名约定

1. **测试类命名**：
   * 格式：`{功能名称}Test.java`
   * 示例：`ClassTreeTest.java`、`ComponentTest.java`

2. **测试资源文件**：
   * CD模型文件：`{测试名称}.cd`
   * 模板文件：保持与主项目一致的命名

3. **测试输出目录**：
   * 统一输出到：`build/generated/test/cd2gui/`

---

## JUnit 测试基础

### 基本注解

* **@Test**：标记测试方法
* **@Ignore**：忽略特定测试
* **@Before**：每个测试方法执行前运行
* **@After**：每个测试方法执行后运行
* **@BeforeClass**：所有测试方法执行前运行一次
* **@AfterClass**：所有测试方法执行后运行一次

### 常用断言方法

* **assertTrue(condition)**：断言条件为真
* **assertFalse(condition)**：断言条件为假
* **assertEquals(expected, actual)**：断言两值相等
* **assertNotNull(object)**：断言对象非空
* **assertNull(object)**：断言对象为空

### 测试方法示例结构

```java
@Test
public void testMethodName() throws Exception {
    // Arrange - 准备测试数据
    String modelPath = "src/test/resources/TestModel.cd";
    String targetPath = TARGET_PATH + "testoutput";
    
    // Act - 执行被测试功能
    CD2GUITool tool = generateGUI(modelPath, targetPath);
    
    // Assert - 验证结果
    assertTrue("生成的文件应该存在", Files.exists(Paths.get(targetPath)));
    assertFalse("不应该包含错误", tool.hasErrors());
}
```

---

## 项目测试架构

### AbstractTest 基类

所有测试类必须继承 `AbstractTest`，该基类提供：

1. **通用常量**：
   * `TARGET_PATH`：测试输出路径
   * `HWC_PATH`：测试资源路径

2. **通用方法**：
   * `generateGUI()`：生成GUI文件
   * `parseDomain()`：解析CD模型
   * `parserTest()`：验证生成的GUI文件

### 测试类型分类

#### 1. 功能测试（Functional Tests）
* **ComponentTest**：基础组件生成测试
* **ClassTreeTest**：类继承树构建测试
* **PartsTest**：不同部件生成测试

#### 2. 边界测试（Edge Case Tests）
* **InvisibleTest**：不可见元素处理测试
* **DerivedTest**：派生类属性继承测试
* **SuperclassTest**：抽象类处理测试

#### 3. 配置测试（Configuration Tests）
* **ExcludeFormTest**：表单排除功能测试
* **TemplateTest**：自定义模板测试

### 测试数据管理

1. **测试模型文件**：
   * 放置在 `src/test/resources/` 目录
   * 使用 MontiCore CD 语法编写
   * 文件名与测试类对应

2. **测试输出**：
   * 统一输出到 `build/generated/test/cd2gui/`
   * 由构建系统自动清理

---

## 测试编写规范

### 测试类结构要求

```java
public class YourFeatureTest extends AbstractTest {
    
    @Test
    public void testMainFunctionality() throws IOException {
        // 测试主要功能
    }
    
    @Test 
    public void testEdgeCases() throws IOException {
        // 测试边界情况
    }
    
    @Test
    public void testErrorHandling() throws IOException {
        // 测试错误处理
    }
}
```

### 测试方法命名规范

* **描述性命名**：`test{功能描述}()`
* **场景命名**：`test{When条件}{Then结果}()`
* **示例**：
  * `testGeneratedFilesExist()`
  * `testWhenInvisibleClassThenNoPageGenerated()`

### 断言最佳实践

1. **提供失败消息**：
   ```java
   assertTrue("生成的文件应该包含属性名", 
              lineList.stream().anyMatch(l -> l.contains("attributeName")));
   ```

2. **分组相关断言**：
   ```java
   // 验证文件存在性
   assertTrue("Details页面应该存在", Files.exists(detailsFile));
   assertTrue("Overview页面应该存在", Files.exists(overviewFile));
   
   // 验证内容正确性
   assertTrue("应该包含属性A", content.contains("attributeA"));
   assertFalse("不应该包含属性B", content.contains("attributeB"));
   ```

---

## 测试执行与验证

### 本地测试执行

```bash
# 执行所有测试
./gradlew test

# 执行特定测试类
./gradlew test --tests "cd2gui.test.parser.ComponentTest"

# 执行特定测试方法
./gradlew test --tests "cd2gui.test.parser.ComponentTest.testDomain"

# 详细输出
./gradlew test --info
```

### 测试报告

* **测试结果**：`build/reports/tests/test/index.html`
* **测试输出**：`build/test-results/test/`
* **覆盖率报告**：（需要配置相关插件）

### 测试验证要求

每个开发人员在提交代码前必须确保：

1. **所有现有测试通过**：
   ```bash
   ./gradlew test
   ```

2. **新功能有对应测试**：
   * 为新增功能编写测试
   * 测试覆盖主要用例和边界情况

3. **测试质量检查**：
   * 测试方法命名清晰
   * 断言包含描述性消息
   * 无冗余或重复测试

---

## CI/CD 集成建议

### 持续集成流程设计

#### 1. 自动化测试流水线

```yaml
# 建议的CI流程结构（伪代码）
stages:
  - build
  - test
  - quality-check
  - deploy

test-job:
  stage: test
  script:
    - ./gradlew clean test
  artifacts:
    reports:
      junit: build/test-results/test/TEST-*.xml
    paths:
      - build/reports/tests/
```

#### 2. 质量门禁策略

* **测试通过率**：100% 通过才能合并
* **代码覆盖率**：新代码覆盖率不低于80%

#### 3. 分支策略集成

* **Pull Request 触发**：每个MR自动执行完整测试套件
* **主分支保护**：只有通过所有测试的代码才能合并到develop分支

### 测试环境配置

#### 1. 环境隔离
* 每个测试在独立的临时目录中执行
* 测试间不共享状态或输出文件
* 自动清理测试产生的临时文件

#### 2. 并行测试支持
* 配置 Gradle 并行测试执行
* 测试类间无依赖关系
* 资源文件使用相对路径引用

---

### 调试技巧

* **日志输出**：使用 Log.info() 输出调试信息
* **断点调试**：在IDE中设置断点逐步调试
* **临时断言**：添加临时断言验证中间状态
* **文件检查**：检查生成的中间文件内容

---

## 测试维护与更新

### 定期维护任务

* **清理过时测试**：删除不再相关的测试用例
* **更新测试数据**：保持测试模型与功能同步
* **重构测试代码**：提取公共测试逻辑到基类
* **性能优化（可选）**：优化慢速测试的执行时间

### 测试文档更新

* 新功能测试必须更新相应文档
* 测试失败时更新故障排查指南
