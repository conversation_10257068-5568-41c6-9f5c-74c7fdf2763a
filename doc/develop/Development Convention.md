# SLE25 Metrics2GUI 项目开发约定（Development Convention）

## 分支管理

### 主分支结构

* `sle25-project-metrics2gui`：**原始主仓库分支**，保持稳定性，仅作为公共同步基础。
* `sle25-cd2gui/develop`：**主开发分支**，集成各成员的稳定代码。
* `sle25-<name>`：**个人开发分支**，每人独立开发，不影响他人。

---

### 合并流程

1. 在本地创建/同步自己的开发分支，确保基于最新 `sle25-cd2gui/develop`。
2. 每个人仅在自己分支中开发任务。
3. 合并前：

   * 确保本地通过构建与测试（如有自动化脚本或验证方法）。
   * Pull 最新的 `sle25-cd2gui/develop` 并解决冲突。
4. 创建 Merge Request（MR）并在群内通知其他人 code review。

---

## 开发原则

### 模块责任划分

* 每位成员应明确自己负责的模块与代码范围，不允许随意修改其他成员负责的代码段。
* 若需协作修改，请提前在群内沟通达成一致。

### 代码隔离

* 每个功能模块应封装清晰，避免交叉调用。
* 工具类、映射规则、UI 插件等建议以包为单位隔离管理。

---

## 命名规范

* 总体遵循 **MontiCore + Java 编码规范**
> 部分参考
* 包名：小写 + 有意义（如 `gui.metrics`, `model.parser`）
* 类名：大驼峰（如 `MetricClassifier`）
* 方法名：小驼峰（如 `classifyMetricByScale()`）
* 常量：全大写 + 下划线（如 `DEFAULT_SCALE`）

---

## 文件结构与组织

### 项目总体结构

本项目遵循标准的 **Gradle + Java** 项目结构：

```
cd2gui/
├── build.gradle                    # 项目构建配置
├── gradle.properties              # Gradle 属性配置
├── README.md                       # 项目说明文档
├── doc/                           # 项目文档目录
│   ├── develop/                   # 开发约定与指南
│   │   ├── Development Convention.md  # 开发约定文档
│   │   └── Testing Guide.md           # 测试指南文档
│   └── *.pptx                     # 项目相关演示文件
├── src/
│   ├── main/
│   │   ├── java/                  # 主要源代码
│   │   │   └── cd2gui/           # 主包结构
│   │   │       ├── CD2GUITool.java          # 主工具类
│   │   │       ├── GuiModelFileCreator.java # GUI模型文件创建器
│   │   │       ├── data/         # 数据模型类
│   │   │       │   ├── CD2GUIAssociation.java
│   │   │       │   ├── CD2GUIAttribute.java
│   │   │       │   └── CD2GUIClassTreeNode.java
│   │   │       └── util/         # 工具类
│   │   │           ├── ASTCDClassManager.java
│   │   │           ├── AttributeManager.java
│   │   │           ├── NameTransformer.java
│   │   │           ├── RoleManager.java
│   │   │           ├── StereotypeManager.java
│   │   │           └── Types.java
│   │   └── resources/            # 资源文件
│   │       └── tpl/              # FreeMarker 模板文件
│   │           ├── dashboard/    # 仪表板模板
│   │           ├── details/      # 详情页模板
│   │           ├── overview/     # 概览页模板
│   │           └── *.ftl         # 其他模板文件
│   └── test/
│       ├── java/                 # 测试源代码
│       │   └── cd2gui/
│       │       └── test/
│       │           └── parser/   # 解析器测试包
│       │               ├── AbstractTest.java    # 测试基类
│       │               ├── ComponentTest.java   # 组件测试
│       │               ├── ClassTreeTest.java   # 类树测试
│       │               ├── DerivedTest.java     # 派生类测试
│       │               ├── ExcludeFormTest.java # 表单排除测试
│       │               ├── InvisibleTest.java   # 不可见元素测试
│       │               ├── PartsTest.java       # 部件测试
│       │               ├── SuperclassTest.java  # 超类测试
│       │               └── TemplateTest.java    # 模板测试
│       └── resources/            # 测试资源文件
│           ├── *.cd              # 测试用CD模型文件
│           └── rpl/              # 模板替换测试文件夹
└── build/                        # 构建输出目录（自动生成）
```

### 文件夹命名约定

（注：仅作为参考，对于简单的模块来说无需如此复杂的结构和内容）

#### 1. 层级编号规范
* **文档目录**可采用两位数字前缀 + 下划线 + 英文名称：
  * `00_requirements/` - 需求分析文档
  * `01_design/` - 架构设计文档  
  * `02_develop/` - 开发相关文档
  * `03_testing/` - 测试相关文档
  * `04_deployment/` - 部署相关文档
  * `99_archive/` - 归档文件
* **当前项目**保持简单结构：`develop/`、演示文件等

#### 2. 代码包命名规范
* **Java 包名**：全小写，使用点分隔，体现功能层次
  * `cd2gui.data` - 数据模型相关
  * `cd2gui.util` - 工具类
  * `cd2gui.parser` - 解析器相关（如需要）
  * `cd2gui.generator` - 生成器相关（如需要）

#### 3. 资源文件夹命名
* **模板文件夹**：按功能分类，使用英文小写
  * `tpl/dashboard/` - 仪表板模板
  * `tpl/details/` - 详情页模板
  * `tpl/overview/` - 概览页模板
* **测试资源**：与被测试模块对应
  * `test/resources/` - 测试用例文件

#### 4. 通用命名规则
* **文件夹名称必须为英文**，禁止使用中文
* **使用小写字母**，单词间用下划线 `_` 或短横线 `-` 分隔
* **避免缩写**，使用完整且有意义的单词
* **保持简洁**，但确保语义清晰

### 新增模块指导

当需要添加新功能模块时：

1. **确定模块位置**：
   * 核心业务逻辑 → `src/main/java/cd2gui/`
   * 工具类 → `src/main/java/cd2gui/util/`
   * 数据模型 → `src/main/java/cd2gui/data/`

2. **创建对应测试**：
   * 测试类 → `src/test/java/cd2gui/test/`
   * 测试资源 → `src/test/resources/`

3. **更新文档**：
   * 模块说明 → `doc/develop/`
   * API 文档 → JavaDoc 注释

---

## 注释与文档

* 所有 **公共方法、类** 必须使用 [JavaDoc](https://www.oracle.com/java/technologies/javase/javadoc-tool.html) 风格注释说明用途。
* 特殊逻辑需在代码中注明用途。
* 每个模块应有独立的 README.md（若功能较复杂），描述用途与调用方式。

---

## 代码组织原则

### 包结构设计
* **按功能分层**：数据模型、业务逻辑、工具类分离
* **避免循环依赖**：高层模块不应依赖低层模块的具体实现
* **接口隔离**：工具类应提供清晰的公共接口

### 文件管理
* **一个类一个文件**：遵循 Java 标准约定
* **相关类就近放置**：功能相关的类应在同一包下
* **测试文件对应**：每个主要类都应有对应的测试类

---

## 工具约定

* IDE：建议统一(不强制，因为任务本身规模不大，其实无所谓)使用 **VSCode 或 IntelliJ**
```
杨东泽：我使用VSCode
```
* 参考工具（可选）：

  * GitLab Issues：任务追踪
  * GitLab Milestones：Sprint 管理
  * Discord/Zoom：语音沟通
  * Slack
  * JavaDoc 插件
  * 为了降低复杂性，上述一些内容可以直接写成Markdown

---

## 提交规范

### Commit message 格式：

```bash
<类型>: <简要说明>
```

#### 类型包括（推荐）：

* `feat`: 新功能
* `fix`: 修复 bug
* `refactor`: 重构代码
* `docs`: 修改文档
* `style`: 格式修改（不涉及功能）
* `test`: 增加或修改测试
* `chore`: 构建流程/依赖修改

#### 示例：

```bash
feat: 添加指标与尺度的自动分类模块
fix: 修复状态枚举未正确生成图表的问题
```

---

## 测试与验证

* 若模块具备独立逻辑，推荐写最小测试（如图表映射规则）
* 每次合并必须能通过现有的构建或运行测试（可视化页面正常展示）
* **强制要求**：每个开发人员必须为自己实现的功能编写对应的 JUnit 测试
* **测试标准**：所有测试必须通过才能提交到 `develop` 分支
* 详细测试规范请参考：[Testing Guide.md](Testing%20Guide.md)

---

## 违反约定处理

* 若合并未遵守约定，review 阶段可拒绝合并，并要求重新提交。
* 若不小心修改他人模块，应及时通知并 revert/修正。

---

## 文件结构维护

### 定期整理原则
* **及时清理**：删除不再使用的临时文件和测试文件
* **合理归档**：完成的里程碑文档移至相应文件夹
* **版本控制**：避免提交 IDE 特定文件和构建产物

### 禁止提交的文件类型
* IDE 配置文件：`.idea/`, `.vscode/settings.json`（除项目共享配置外）
* 构建产物：`build/`, `target/`, `*.class`
* 临时文件：`*.tmp`, `*.log`, `*.cache`
* 个人配置：本地环境变量、个人笔记等

### 文件夹权限
* **核心模块**（`src/main/`）：需要团队 review 后方可修改
* **测试代码**（`src/test/`）：可独立添加，但不应影响现有测试
* **文档目录**（`doc/`）：鼓励补充，但需保持结构一致