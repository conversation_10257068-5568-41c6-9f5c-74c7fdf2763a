# CD2GUI Project Summary

**Project Name**: CD2GUI - Class Diagram to GUI Metrics Visualization  
**Version**: 1.0.0  
**Created**: 2025-07-24

## 1. Project Overview

CD2GUI is a GUI metrics visualization tool based on UML class diagrams that automatically analyzes class diagram structures and generates GUI pages with charts. The project implements a complete workflow from class diagram analysis to FTL template generation, supporting 4 core chart types for visualization.

### 1.1 Core Features
- **Stage2**: Class diagram attribute analysis and metrics calculation
- **Stage3**: GUI model file generation and FTL template creation
- **Chart Support**: Pie charts, bar charts, line charts, scatter plots
- **Pattern Recognition**: 8 patterns including geographic distribution, categorical distribution, performance analysis, academic scoring, etc.

### 1.2 Technology Stack
- **Language**: Java 11+
- **Template Engine**: FreeMarker (FTL)
- **Chart Library**: mc.fenix.charts (GemChart series)
- **Build Tool**: Gradle
- **Architecture**: MontiCore + UMLP-Tool

## 2. System Architecture

### 2.1 High-Level Architecture

```mermaid
graph TB
    subgraph "CD2GUI System"
        A[CD2GUITool<br/>Main Entry] --> B[GuiModelFileCreator<br/>GUI Model Creator]
        A --> C[FTLGenerator<br/>FTL Template Generator]
        
        B --> D[AttributeMetricIdentifier<br/>Metric Identifier]
        D --> E[UnifiedAttributeAnalyzer<br/>Unified Analysis Engine]
        
        E --> F[AttributeMetric<br/>Metric Data]
        F --> G[ChartType + ChartDetail<br/>Chart Specification]
        
        C --> H[FTL Templates<br/>Template Files]
    end
    
    subgraph "External Dependencies"
        I[MontiCore AST] --> B
        J[UMLP-Tool] --> H
        H --> K[Generated Java Code]
        K --> L[Frontend GUI]
    end
    
    style A fill:#e1f5fe
    style E fill:#f3e5f5
    style H fill:#e8f5e8
```

### 2.2 Data Flow Diagram

```mermaid
flowchart LR
    subgraph "Input"
        A1[.cd Files<br/>Class Diagram Files]
    end
    
    subgraph "Stage2: Analysis"
        B1[cd2gui.metric.AttributeMetricIdentifier<br/>processClassForVisualization]
        B2[cd2gui.metric.UnifiedAttributeAnalyzer<br/>analyzeClassForVisualization]
        B3[Pattern Recognition<br/>8 Pattern Types]
        B4[cd2gui.data.AttributeMetric<br/>Metric Results]
    end
    
    subgraph "Stage3: Generation"
        C1[cd2gui.GuiModelFileCreator<br/>initializeMetrics]
        C2[cd2gui.ftl.FTLGenerator<br/>generateFTLTemplates]
        C3[FTL Template Files<br/>Chart Templates]
    end
    
    subgraph "Output"
        D1[.gui Files<br/>GUI Pages]
        D2[Generated Methods<br/>Data Access Methods]
    end
    
    A1 --> B1
    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> D1
    C3 --> D2
    
    style B2 fill:#fff3e0
    style C2 fill:#e8f5e8
```

## 3. Core Components

### 3.1 Data Type Handling

#### Chart Data Type Adaptation
CD2GUI implements strict data type matching mechanisms to ensure compatibility with underlying chart library APIs:

- **PieChartDetail**: Uses `int value` parameter, compatible with `GemPieChartEntryBuilder`
- **BarChartDetail**: Uses `List<Integer> data` parameter, compatible with `GemBarChartEntryBuilder`  
- **LineChartDetail**: Uses `List<Double> data` parameter, compatible with `GemLineChartEntryBuilder`
- **ScatterPlotDetail**: Uses `double x, double y` parameters, compatible with `GemScatterPlotPoint`

#### Type Conversion Handling
The system ensures type matching at the interface design level, avoiding runtime conversions:
- Line chart data directly uses `List<Double>` type, no type conversion needed
- Scatter plot data uses native `double` type, ensuring precision
- Pie and bar charts use integer types, conforming to counting statistics semantics

### 3.2 Stage2: Analysis Engine

#### UnifiedAttributeAnalyzer.java
- **Responsibility**: Core analysis engine implementing 8 pattern recognition types
- **Key Methods**:
  - `analyzeClassForVisualization()`: Main analysis entry point
  - `generateCombinationCharts()`: Combination chart generation
  - `generateSingleAttributeCharts()`: Single attribute chart generation

#### Supported Pattern Recognition
1. **Geographic Distribution Pattern**: Address, city, country geographic information
2. **Categorical Distribution Pattern**: Enum types, status fields
3. **Performance Analysis Pattern**: Scores, ratings, performance metrics
4. **Academic Scoring Pattern**: Grades, scores, ratings
5. **User Management Pattern**: User roles, permission analysis
6. **Project Management Pattern**: Project status, progress analysis
7. **Temporal Series Pattern**: Date, timestamp data
8. **Correlation Analysis Pattern**: Relationships between numeric attributes

### 3.3 Stage3: Generation Engine

#### FTLGenerator.java
- **Responsibility**: Generate UMLP-Tool compatible FTL templates with dual output mechanism
- **Output Directories**: 
  - `build/generated/ftl/` - Primary output directory
  - `backend/src/main/resources/ftl/` - UMLP-Tool usage directory
- **Supported Charts**:
  - `generatePieChartTemplate()`: Pie chart template, generates categorical data statistics logic
  - `generateBarChartTemplate()`: Bar chart template, generates distribution statistics logic
  - `generateLineChartTemplate()`: Line chart template, generates temporal data processing logic
  - `generateScatterPlotTemplate()`: Scatter plot template, generates numerical distribution logic
- **Auto-copy Mechanism**: `writeTemplateFile()` method writes to both directories simultaneously, ensuring UMLP-Tool can access templates correctly

## 4. Usage Guide

### 4.1 Integration in Real Projects

#### Step 1: Add Chart Imports
Add chart component imports to your project's `.gui` files:

```java
// Add to existing imports
import mc.fenix.charts.GemPieChart;
import mc.fenix.charts.GemScatterPlot;
import mc.fenix.charts.GemBarChart;
import mc.fenix.charts.GemLineChart;
```

#### Step 2: Add Chart Components
Follow the modification pattern from the car-rental project:

```java
// Original code - vehicle list only
@GemText(value = getAvailableCarsTitle(), size = "2rem", weight = "bold"),
@iterate(Car c : cars) {
  // Vehicle list...
}

// Modified code - add chart section
@GemText(value = getAvailableCarsTitle(), size = "2rem", weight = "bold"),

// New chart section
@GemRow(colGap = "20px", components = [
  @GemCard(
    width = "45%",
    title = "Car Status Distribution",
    component = @GemPieChart(data = processDataForCarStatus(), innerRadius = 50)
  ),
  @GemCard(
    width = "45%", 
    title = "Car Manufacturer Distribution",
    component = @GemPieChart(data = processDataForCarManufacturer(), innerRadius = 50)
  )
]),

@GemCard(
  width = "90%",
  title = "Car Mileage Distribution", 
  component = @GemScatterPlot(data = processDataForCarMileage())
),

@iterate(Car c : cars) {
  // Original vehicle list...
}
```

#### Step 3: Configure Gradle Memory Management
Add to your project's `gradle.properties` file:

```properties
# JVM memory settings to prevent OutOfMemoryError
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=1g -XX:+UseG1GC
```

### 4.2 Generated Data Access Methods

CD2GUI automatically generates methods in the following formats:

```java
// Pie chart data methods (actual car-rental project naming)
public GemPieChartData processDataForCarStatus()
public GemPieChartData processDataForCarManufacturer()

// Scatter plot data methods  
public GemScatterPlotData processDataForCarMileage()

// FTL generator generated method naming format
public GemPieChartData getPieChartData4{ClassName}{AttributeName}()
public GemBarChartData getBarChartData4{ClassName}{AttributeName}()
public GemLineChartData getLineChartData4{ClassName}{AttributeName}()
public GemScatterPlotData getScatterPlotData4{ClassName}{AttributeName}()
```

## 5. Project File Structure

```
cd2gui/
├── src/main/java/cd2gui/
│   ├── CD2GUITool.java                 # Main entry point
│   ├── GuiModelFileCreator.java        # GUI model creator
│   ├── data/                           # Data models
│   │   ├── AttributeMetric.java        # Metric data container
│   │   ├── ChartType.java             # Chart type enumeration
│   │   ├── *ChartDetail.java          # Chart detail implementations
│   │   └── ClassMetrics.java          # Class metrics container
│   ├── ftl/                           # FTL generator
│   │   └── FTLGenerator.java          # Template generator
│   ├── metric/                        # Metric analysis
│   │   ├── AttributeMetricIdentifier.java  # Metric identifier
│   │   └── UnifiedAttributeAnalyzer.java   # Analysis engine
│   └── util/                          # Utility classes
│       ├── CD2GUIAttribute.java       # Attribute wrapper
│       └── Types.java                 # Type utilities
└── src/main/resources/tpl/            # FTL template resources
    └── metrics/                       # Chart templates
        ├── charts/                    # Specific chart templates
        └── *.ftl                      # Page templates
```

## 6. Detailed Technical Workflow

### 6.1 Complete Data Flow Process

```mermaid
sequenceDiagram
    participant User as User
    participant Tool as CD2GUITool
    participant Creator as GuiModelFileCreator
    participant Identifier as AttributeMetricIdentifier
    participant Analyzer as UnifiedAttributeAnalyzer
    participant Generator as FTLGenerator
    participant Output as Output Files

    User->>Tool: Call generateGUI()
    Tool->>Creator: Create GuiModelFileCreator instance
    Creator->>Creator: initializeAttributesAndRoles()
    Creator->>Creator: initializeMetrics()
    Creator->>Identifier: processClassForVisualization()

    loop For each class attribute
        Identifier->>Analyzer: analyzeClassForVisualization()
        Analyzer->>Analyzer: Pattern recognition (8 patterns)
        Analyzer->>Analyzer: Create AttributeMetric
        Analyzer-->>Identifier: Return metric results
    end

    Identifier-->>Creator: Return all metric data
    Creator->>Creator: Generate various GUI pages
    Creator-->>Tool: Page generation complete

    Tool->>Generator: generateFTLTemplates()

    loop For each AttributeMetric
        Generator->>Generator: Generate template by ChartType
        Generator->>Output: Write FTL template file
    end

    Generator-->>Tool: FTL generation complete
    Tool-->>User: Complete workflow finished
```

### 6.2 Chart Type Decision Flow

```mermaid
flowchart TD
    A[Attribute Analysis Start] --> B{Attribute Type Check}

    B -->|Enum Type| C[Categorical Distribution Pattern]
    B -->|Numeric Type| D{Numeric Range Analysis}
    B -->|String Type| E{String Pattern Recognition}
    B -->|Date Type| F[Temporal Series Pattern]

    C --> G[PIE_CHART<br/>Pie Chart]

    D -->|0-100 Range| H[Performance/Academic Pattern]
    D -->|Other Numeric| I[Correlation Analysis Pattern]

    E -->|Geographic Keywords| J[Geographic Distribution Pattern]
    E -->|Role Keywords| K[User Management Pattern]
    E -->|Status Keywords| L[Project Management Pattern]
    E -->|Other| M[Categorical Distribution Pattern]

    F --> N[LINE_CHART<br/>Line Chart]
    H --> O[BAR_CHART<br/>Bar Chart]
    I --> P[SCATTER_PLOT<br/>Scatter Plot]
    J --> G
    K --> G
    L --> G
    M --> G

    style G fill:#ffeb3b
    style N fill:#4caf50
    style O fill:#2196f3
    style P fill:#ff9800
```

### 6.3 FTL Template Generation Mechanism

```mermaid
graph LR
    subgraph "FTL Template Generation"
        A[AttributeMetric] --> B["ChartType Decision"]

        B -->|PIE_CHART| C[generatePieChartTemplate]
        B -->|BAR_CHART| D[generateBarChartTemplate]
        B -->|LINE_CHART| E[generateLineChartTemplate]
        B -->|SCATTER_PLOT| F[generateScatterPlotTemplate]

        C --> G["processDataFor(Class)(Attr).ftl"]
        D --> H["getBarChartData4(Class)(Attr).ftl"]
        E --> I["getLineChartData4(Class)(Attr).ftl"]
        F --> J["getScatterPlotData4(Class)(Attr).ftl"]

        G --> K[UMLP-Tool Processing]
        H --> K
        I --> K
        J --> K

        K --> L[Generate Java Methods]
        L --> M[GUI Page Invocation]
    end

    style C fill:#ffeb3b
    style D fill:#2196f3
    style E fill:#4caf50
    style F fill:#ff9800
```

## 7. Real-World Use Case

### 7.1 Car-Rental Project Integration Example

![alt text](205155b4e2d04db62cecee38fb6fc932.png)

#### New Chart Features
1. **Car Status Distribution** (Pie Chart): Shows vehicle status distribution
2. **Car Manufacturer Distribution** (Pie Chart): Shows manufacturer distribution
3. **Car Mileage Distribution** (Scatter Plot): Shows mileage distribution

#### Generated Data Methods
```java
// Automatically generated by CD2GUI, verified to work correctly
public GemPieChartData processDataForCarStatus() {
    // Analyzes Car.status enum attribute, counts vehicles by status
    List<Car> cars = getCars();
    Map<String, Integer> statusCount = new HashMap<>();
    // Complete data statistics logic implementation
}

public GemPieChartData processDataForCarManufacturer() {
    // Analyzes Car.manufacturer string attribute, counts manufacturer distribution
    List<Car> cars = getCars();
    Map<String, Integer> distribution = new HashMap<>();
    // Complete distribution statistics logic implementation
}

public GemScatterPlotData processDataForCarMileage() {
    // Analyzes Car.mileage numeric attribute, generates scatter plot data
    List<Car> cars = getCars();
    // Complete numerical distribution logic implementation
}
```

#### Auto-generated FTL Templates
The system successfully generates the following FTL template files:
- `getPieChartData4CarStatus.ftl`
- `getPieChartData4CarManufacturer.ftl`
- `getScatterPlotData4CarMileage.ftl`

Templates are automatically deployed to two locations:
- `build/generated/ftl/` - Primary output directory
- `backend/src/main/resources/ftl/` - UMLP-Tool usage directory

### 7.2 Memory Configuration Importance

For large projects, FTL template generation and chart rendering require sufficient memory:

```properties
# Required Gradle memory configuration
org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=1g -XX:+UseG1GC

# Explanation:
# -Xmx4g: Maximum heap memory 4GB
# -XX:MaxMetaspaceSize=1g: Maximum metaspace 1GB
# -XX:+UseG1GC: Use G1 garbage collector
```

## 8. Development and Maintenance Guide

### 8.1 Adding New Chart Types

1. **Extend ChartType enumeration**
2. **Create corresponding ChartDetail implementation**
3. **Add creation method in UnifiedAttributeAnalyzer**
4. **Add template generation method in FTLGenerator**

### 8.2 Adding New Pattern Recognition

1. **Add recognition logic in UnifiedAttributeAnalyzer**
2. **Define keywords and judgment conditions**
3. **Select appropriate chart type**
4. **Add test cases for verification**

### 8.3 Key Technical Points

#### FTL Template Dual Output Mechanism
- **Problem**: UMLP-Tool needs to read FTL templates from specific directories
- **Solution**: `FTLGenerator.writeTemplateFile()` writes to both directories simultaneously
- **Implementation**: Auto-copy mechanism ensures templates are available in correct locations