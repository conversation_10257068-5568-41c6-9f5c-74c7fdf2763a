# (c) https://github.com/MontiCore/monticore
image: registry.git.rwth-aachen.de/monticore/container-registry/gradle:7.6.4-jdk11

variables:
  GRADLE_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2"

cache:
  paths:
    - .gradle/wrapper
  key: ${CI_COMMIT_REF_SLUG}

before_script:
  - export GRADLE_USER_HOME=`pwd`/.gradle

stages:
  - build
  - test
  - deploy

build:
  stage: build
  variables:
    CI_DEBUG_TRACE: "true"
  script:
    - "gradle build --info --refresh-dependencies -P mavenPassword=$password -P mavenUser=$username $GRADLE_OPTS $ARGS1"
  artifacts:
    paths:
      - "."
    expire_in: 1 week

test:
  stage: test
  dependencies:
    - build
  script:
    - "gradle test -P mavenPassword=$password -P mavenUser=$username $GRADLE_OPTS $ARGS1"

deploy:
  stage: deploy
  dependencies:
    - test
  script:
    - "gradle publish -P mavenPassword=$password -P mavenUser=$username $GRADLE_OPTS $ARGS3"
  only:
    - dev
    - master
